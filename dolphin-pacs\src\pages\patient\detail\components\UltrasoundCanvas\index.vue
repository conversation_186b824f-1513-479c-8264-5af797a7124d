<script lang="ts" setup>
import { ref, nextTick } from "vue"
import * as fabric from 'fabric'
import { ElMessage } from "element-plus"
import CanvasToolbar from './components/CanvasToolbar.vue'
import FabricCanvas from './components/FabricCanvas.vue'
import ImagePanel from './components/ImagePanel.vue'

defineOptions({
  name: "UltrasoundCanvas"
})

// 定义事件
const emit = defineEmits<{
  analyzeImage: [imageData: any]
}>()

// 组件引用
const fabricCanvasRef = ref<InstanceType<typeof FabricCanvas>>()

// 画布状态
const isLoading = ref(true)
const canvasReady = ref(false)
const showGrid = ref(true)
const zoomLevel = ref(1)

// 工具栏状态
const toolbarVisible = ref(true)

// 图像列表状态
const imageList = ref<Array<{
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}>>([])
const selectedImageId = ref<string>('')

// 画布就绪处理
const handleCanvasReady = (canvas: fabric.Canvas) => {
  canvasReady.value = true
  isLoading.value = false
  ElMessage.success('画布加载完成')
}

// 缩放变化处理
const handleZoomChanged = (zoom: number) => {
  zoomLevel.value = zoom
}

// 缩放控制
const zoomIn = () => {
  fabricCanvasRef.value?.zoomIn()
}

const zoomOut = () => {
  fabricCanvasRef.value?.zoomOut()
}

const resetZoom = () => {
  fabricCanvasRef.value?.resetZoom()
}

const fitToCanvas = () => {
  fabricCanvasRef.value?.fitToCanvas()
}

// 切换网格显示
const toggleGrid = () => {
  showGrid.value = !showGrid.value
}

// 生成缩略图
const generateThumbnail = (imgElement: HTMLImageElement, maxSize = 100): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')!

  const { width, height } = imgElement
  const scale = Math.min(maxSize / width, maxSize / height)

  canvas.width = width * scale
  canvas.height = height * scale

  ctx.drawImage(imgElement, 0, 0, canvas.width, canvas.height)
  return canvas.toDataURL()
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 加载图像
const loadImage = (file: File) => {
  const fabricCanvas = fabricCanvasRef.value?.fabricCanvas()
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    const imgUrl = e.target?.result as string
    const img = new Image()
    img.onload = () => {
      const fabricImg = new (fabric as any).Image(img, {
        left: fabricCanvas.getWidth() / 2,
        top: fabricCanvas.getHeight() / 2,
        originX: 'center',
        originY: 'center'
      })

      // 如果图像太大，缩放到合适大小
      const maxSize = Math.min(fabricCanvas.getWidth(), fabricCanvas.getHeight()) * 0.8
      if (fabricImg.width! > maxSize || fabricImg.height! > maxSize) {
        const scale = maxSize / Math.max(fabricImg.width!, fabricImg.height!)
        fabricImg.scale(scale)
      }

      // 生成唯一ID
      const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      fabricImg.set('imageId', imageId)

      fabricCanvasRef.value?.addImage(fabricImg)

      // 添加到图像列表
      const imageInfo = {
        id: imageId,
        name: file.name,
        size: formatFileSize(file.size),
        format: file.type.split('/')[1].toUpperCase(),
        uploadTime: new Date().toLocaleString('zh-CN'),
        thumbnail: generateThumbnail(img),
        fabricObject: fabricImg
      }

      imageList.value.push(imageInfo)
      selectedImageId.value = imageId

      ElMessage.success('图像加载成功')
    }
    img.onerror = () => {
      ElMessage.error('图像加载失败')
    }
    img.src = imgUrl
  }
  reader.readAsDataURL(file)
}

// 清空画布
const clearCanvas = () => {
  fabricCanvasRef.value?.clearCanvas()
  imageList.value = []
  selectedImageId.value = ''
  ElMessage.success('画布已清空')
}

// 选择图像
const selectImage = (imageId: string) => {
  selectedImageId.value = imageId
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    fabricCanvasRef.value?.setActiveObject(imageInfo.fabricObject)
  }
}

// 删除图像
const deleteImage = (imageId: string) => {
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    fabricCanvasRef.value?.removeImage(imageInfo.fabricObject)
  }

  imageList.value = imageList.value.filter(img => img.id !== imageId)

  if (selectedImageId.value === imageId) {
    selectedImageId.value = imageList.value.length > 0 ? imageList.value[0].id : ''
  }

  ElMessage.success('图像已删除')
}

// 显示/隐藏图像
const toggleImageVisibility = (imageId: string) => {
  const imageInfo = imageList.value.find(img => img.id === imageId)
  if (imageInfo && imageInfo.fabricObject) {
    const isVisible = imageInfo.fabricObject.visible
    imageInfo.fabricObject.set('visible', !isVisible)
    fabricCanvasRef.value?.fabricCanvas()?.renderAll()
  }
}

// 分析图像
const analyzeImage = (imageId: string) => {
  const imageInfo = imageList.value.find((img: any) => img.id === imageId)
  if (imageInfo) {
    try {
      const imageData = {
        id: imageInfo.id,
        name: imageInfo.name,
        size: imageInfo.size,
        format: imageInfo.format,
        uploadTime: imageInfo.uploadTime,
        thumbnail: imageInfo.thumbnail
      }

      emit('analyzeImage', imageData)
      ElMessage.success('正在切换到图像分析...')
    } catch (error) {
      console.error('分析失败:', error)
      ElMessage.error('分析失败，请重试')
    }
  }
}

// 加载示例图片
const loadSampleImage = () => {
  const fabricCanvas = fabricCanvasRef.value?.fabricCanvas()
  if (!fabricCanvas) {
    ElMessage.error('画布未初始化')
    return
  }

  // 创建示例图片
  const canvas = document.createElement('canvas')
  canvas.width = 400
  canvas.height = 300
  const ctx = canvas.getContext('2d')!

  // 绘制黑色背景
  ctx.fillStyle = '#000000'
  ctx.fillRect(0, 0, 400, 300)

  // 绘制模拟的超声图像内容
  ctx.fillStyle = '#333333'
  ctx.fillRect(50, 50, 300, 200)

  // 添加白色的模拟结构
  ctx.fillStyle = '#ffffff'
  ctx.beginPath()
  ctx.arc(200, 150, 30, 0, 2 * Math.PI)
  ctx.fill()

  ctx.fillStyle = '#cccccc'
  ctx.fillRect(100, 100, 200, 20)
  ctx.fillRect(120, 180, 160, 15)

  // 添加扫描线效果
  ctx.strokeStyle = '#666666'
  ctx.lineWidth = 1
  for (let i = 0; i < 400; i += 10) {
    ctx.beginPath()
    ctx.moveTo(i, 50)
    ctx.lineTo(i, 250)
    ctx.stroke()
  }

  // 转换为图片并添加到画布
  const dataURL = canvas.toDataURL()
  const img = new Image()
  img.onload = () => {
    const fabricImg = new (fabric as any).Image(img, {
      left: fabricCanvas.getWidth() / 2,
      top: fabricCanvas.getHeight() / 2,
      originX: 'center',
      originY: 'center'
    })

    const imageId = `sample_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    fabricImg.set('imageId', imageId)

    fabricCanvasRef.value?.addImage(fabricImg)

    const imageInfo = {
      id: imageId,
      name: '超声图像_示例.jpg',
      size: '约 15 KB',
      format: 'JPG',
      uploadTime: new Date().toLocaleString('zh-CN'),
      thumbnail: generateThumbnail(img),
      fabricObject: fabricImg
    }

    imageList.value.push(imageInfo)
    selectedImageId.value = imageId

    ElMessage.success('示例图像加载成功')
  }
  img.src = dataURL
}
</script>

<template>
  <div class="ultrasound-canvas">
    <!-- 工具栏 -->
    <CanvasToolbar
      v-if="canvasReady"
      :zoom-level="zoomLevel"
      :show-grid="showGrid"
      :visible="toolbarVisible"
      @zoom-in="zoomIn"
      @zoom-out="zoomOut"
      @reset-zoom="resetZoom"
      @fit-canvas="fitToCanvas"
      @toggle-grid="toggleGrid"
      @load-image="loadImage"
      @clear-canvas="clearCanvas"
      @load-sample="loadSampleImage"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 画布容器 -->
      <FabricCanvas
        ref="fabricCanvasRef"
        :show-grid="showGrid"
        :is-loading="isLoading"
        @canvas-ready="handleCanvasReady"
        @zoom-changed="handleZoomChanged"
      />

      <!-- 图像列表面板 -->
      <ImagePanel
        :image-list="imageList"
        :selected-image-id="selectedImageId"
        @select-image="selectImage"
        @delete-image="deleteImage"
        @toggle-visibility="toggleImageVisibility"
        @analyze-image="analyzeImage"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ultrasound-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    overflow: hidden;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .ultrasound-canvas {
    .main-content {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style>
