<script lang="ts" setup>
import ZoomControls from './ZoomControls.vue'
import GridToggle from './GridToggle.vue'
import FileOperations from './FileOperations.vue'

defineOptions({
  name: "CanvasToolbar"
})

interface Props {
  zoomLevel: number
  showGrid: boolean
  visible?: boolean
}

withDefaults(defineProps<Props>(), {
  visible: true
})

const emit = defineEmits<{
  zoomIn: []
  zoomOut: []
  resetZoom: []
  fitCanvas: []
  toggleGrid: []
  loadImage: [file: File]
  clearCanvas: []
  loadSample: []
}>()
</script>

<template>
  <div v-if="visible" class="toolbar">
    <div class="toolbar-section">
      <ZoomControls
        :zoom-level="zoomLevel"
        @zoom-in="emit('zoomIn')"
        @zoom-out="emit('zoomOut')"
        @reset-zoom="emit('resetZoom')"
        @fit-canvas="emit('fitCanvas')"
      />
    </div>

    <div class="toolbar-section">
      <GridToggle
        :show-grid="showGrid"
        @toggle="emit('toggleGrid')"
      />
    </div>

    <div class="toolbar-section">
      <FileOperations
        @load-image="emit('loadImage', $event)"
        @clear-canvas="emit('clearCanvas')"
        @load-sample="emit('loadSample')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
  flex-wrap: wrap;

  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .toolbar {
    padding: 8px 12px;
    gap: 12px;

    .toolbar-section {
      gap: 6px;
    }

    :deep(.el-button) {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
}
</style>
